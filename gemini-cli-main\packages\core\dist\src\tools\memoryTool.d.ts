/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { BaseTool, ToolR<PERSON>ult } from './tools.js';
export declare const ARIEN_AI_CONFIG_DIR = ".arien-ai";
export declare const DEFAULT_CONTEXT_FILENAME = "ARIEN-AI.md";
export declare const MEMORY_SECTION_HEADER = "## Arien-AI Added Memories";
export declare function setArienAiMdFilename(newFilename: string | string[]): void;
export declare function getCurrentArienAiMdFilename(): string;
export declare function getAllArienAiMdFilenames(): string[];
export declare const setGeminiMdFilename: typeof setArienAiMdFilename;
export declare const getCurrentGeminiMdFilename: typeof getCurrentArienAiMdFilename;
export declare const getAllGeminiMdFilenames: typeof getAllArienAiMdFilenames;
export declare const GEMINI_CONFIG_DIR = ".arien-ai";
interface SaveMemoryParams {
    fact: string;
}
export declare class MemoryTool extends BaseTool<SaveMemoryParams, ToolResult> {
    static readonly Name: string;
    constructor();
    static performAddMemoryEntry(text: string, memoryFilePath: string, fsAdapter: {
        readFile: (path: string, encoding: 'utf-8') => Promise<string>;
        writeFile: (path: string, data: string, encoding: 'utf-8') => Promise<void>;
        mkdir: (path: string, options: {
            recursive: boolean;
        }) => Promise<string | undefined>;
    }): Promise<void>;
    execute(params: SaveMemoryParams, _signal: AbortSignal): Promise<ToolResult>;
}
export {};
