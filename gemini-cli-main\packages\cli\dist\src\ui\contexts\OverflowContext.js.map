{"version": 3, "file": "OverflowContext.js", "sourceRoot": "", "sources": ["../../../../src/ui/contexts/OverflowContext.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EACZ,aAAa,EACb,UAAU,EACV,QAAQ,EACR,WAAW,EACX,OAAO,GACR,MAAM,OAAO,CAAC;AAWf,MAAM,oBAAoB,GAAG,aAAa,CACxC,SAAS,CACV,CAAC;AAEF,MAAM,sBAAsB,GAAG,aAAa,CAC1C,SAAS,CACV,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAA8B,EAAE,CAC9D,UAAU,CAAC,oBAAoB,CAAC,CAAC;AAEnC,MAAM,CAAC,MAAM,kBAAkB,GAAG,GAAgC,EAAE,CAClE,UAAU,CAAC,sBAAsB,CAAC,CAAC;AAErC,MAAM,CAAC,MAAM,gBAAgB,GAA4C,CAAC,EACxE,QAAQ,GACT,EAAE,EAAE;IACH,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,EAAU,CAAC,CAAC;IAExE,MAAM,gBAAgB,GAAG,WAAW,CAAC,CAAC,EAAU,EAAE,EAAE;QAClD,iBAAiB,CAAC,CAAC,OAAO,EAAE,EAAE;YAC5B,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;gBACpB,OAAO,OAAO,CAAC;YACjB,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;YAChC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACf,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,mBAAmB,GAAG,WAAW,CAAC,CAAC,EAAU,EAAE,EAAE;QACrD,iBAAiB,CAAC,CAAC,OAAO,EAAE,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC;YACjB,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAClB,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,UAAU,GAAG,OAAO,CACxB,GAAG,EAAE,CAAC,CAAC;QACL,cAAc;KACf,CAAC,EACF,CAAC,cAAc,CAAC,CACjB,CAAC;IAEF,MAAM,YAAY,GAAG,OAAO,CAC1B,GAAG,EAAE,CAAC,CAAC;QACL,gBAAgB;QAChB,mBAAmB;KACpB,CAAC,EACF,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CACxC,CAAC;IAEF,OAAO,CACL,KAAC,oBAAoB,CAAC,QAAQ,IAAC,KAAK,EAAE,UAAU,YAC9C,KAAC,sBAAsB,CAAC,QAAQ,IAAC,KAAK,EAAE,YAAY,YACjD,QAAQ,GACuB,GACJ,CACjC,CAAC;AACJ,CAAC,CAAC"}