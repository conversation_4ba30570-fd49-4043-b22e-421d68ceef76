/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { render } from 'ink';
import { AppWrapper } from './ui/App.js';
import { loadCliConfig } from './config/config.js';
import { readStdin } from './utils/readStdin.js';
import { basename } from 'node:path';
import v8 from 'node:v8';
import os from 'node:os';
import crypto from 'node:crypto';
import { spawn } from 'node:child_process';
import { start_sandbox } from './utils/sandbox.js';
import {
  LoadedSettings,
  loadSettings,
  SettingScope,
} from './config/settings.js';
import { themeManager } from './ui/themes/theme-manager.js';
import { getStartupWarnings } from './utils/startupWarnings.js';
import { runNonInteractive } from './nonInteractiveCli.js';
import { loadExtensions, Extension } from './config/extension.js';
import { cleanupCheckpoints } from './utils/cleanup.js';
import {
  ApprovalMode,
  Config,
  EditTool,
  FileDiscoveryService,
  GitService,
  initializeTelemetry,
  MemoryTool,
  ReadFileTool,
  ShellTool,
  ToolRegistry,
  WriteFileTool,
  GrepTool,
  GlobTool,
  LSTool,
  WebFetchTool,
  WebSearchTool,
  ReadManyFilesTool,

  DiscoveredTool,
  TelemetryTarget,
} from '@arien-ai/arien-ai-core';

export async function main() {
  const startTime = Date.now();

  // Cleanup old checkpoints on startup
  cleanupCheckpoints();

  const args = process.argv.slice(2);

  // Handle version flag
  if (args.includes('--version') || args.includes('-v')) {
    console.log(process.env.CLI_VERSION || 'unknown');
    process.exit(0);
  }

  // Handle help flag
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Arien-AI CLI - AI-powered command line interface

Usage: arien-ai [options] [prompt]

Options:
  -h, --help              Show this help message
  -v, --version           Show version number
  --non-interactive       Run in non-interactive mode
  --debug                 Enable debug mode
  --approval-mode <mode>  Set approval mode (default, yolo)
  --model <model>         Specify the AI model to use
  --theme <theme>         Set the color theme
  --sandbox <type>        Set sandbox type (docker, podman, false)

Examples:
  arien-ai                           # Start interactive mode
  arien-ai "explain this code"       # Run single command
  arien-ai --debug "analyze logs"    # Run with debug output

For more information, visit: https://github.com/arien-ai/arien-ai-cli
`);
    process.exit(0);
  }

  // Load settings first to determine sandbox configuration
  const loadedSettings = loadSettings(process.cwd());
  const settings = loadedSettings.merged;

  // Initialize theme manager early
  themeManager.setActiveTheme(settings.theme || 'default');

  // Check for non-interactive mode
  const nonInteractiveIndex = args.indexOf('--non-interactive');
  if (nonInteractiveIndex !== -1) {
    args.splice(nonInteractiveIndex, 1);
    const prompt = args.join(' ');
    if (!prompt) {
      console.error('Error: --non-interactive requires a prompt');
      process.exit(1);
    }

    // Load extensions and create config for non-interactive mode
    const extensions: Extension[] = loadExtensions(process.cwd());
    const sessionId = crypto.randomUUID();
    const config = await loadCliConfig(settings, extensions, sessionId);

    await runNonInteractive(config, prompt);
    return;
  }

  // Handle sandbox startup
  if (settings.sandbox !== false && settings.sandbox !== 'false') {
    try {
      const { loadSandboxConfig } = await import('./config/sandboxConfig.js');
      const sandboxConfig = await loadSandboxConfig(settings, { sandbox: settings.sandbox });

      if (sandboxConfig) {
        await start_sandbox(sandboxConfig, process.argv.slice(1));
        // If we reach here, we're in the parent process and sandbox failed to start
        console.error('Failed to start sandbox');
        process.exit(1);
      }
    } catch (error) {
      console.error('Sandbox startup error:', error);
      process.exit(1);
    }
  }

  // Load extensions
  const extensions: Extension[] = loadExtensions(process.cwd());

  // Generate session ID
  const sessionId = crypto.randomUUID();

  // Load configuration
  const config = await loadCliConfig(settings, extensions, sessionId);

  // Initialize telemetry
  initializeTelemetry(config);

  // Create tool registry
  const toolRegistry = new ToolRegistry(config);

  // Register core tools
  const fileDiscoveryService = new FileDiscoveryService(config.getTargetDir());
  const gitService = new GitService(config.getTargetDir());
  const memoryTool = new MemoryTool();

  toolRegistry.registerTool(new ReadFileTool(config.getTargetDir(), config));
  toolRegistry.registerTool(new WriteFileTool(config));
  toolRegistry.registerTool(new EditTool(config));
  toolRegistry.registerTool(new ShellTool(config));
  toolRegistry.registerTool(new GrepTool(config.getTargetDir()));
  toolRegistry.registerTool(new GlobTool(config.getTargetDir(), config));
  toolRegistry.registerTool(new LSTool(config.getTargetDir(), config));
  toolRegistry.registerTool(new WebFetchTool(config));
  toolRegistry.registerTool(new WebSearchTool(config));
  toolRegistry.registerTool(new ReadManyFilesTool(config.getTargetDir(), config));
  toolRegistry.registerTool(memoryTool);

  // Discover tools (including MCP tools)
  try {
    await toolRegistry.discoverTools();
  } catch (error) {
    console.warn('Failed to discover tools:', error);
  }

  // Get startup warnings
  const warnings = await getStartupWarnings();

  // Read stdin if available
  const stdinContent = await readStdin();

  // Calculate startup time
  const startupTime = Date.now() - startTime;

  // Render the app
  const { unmount } = render(
    <AppWrapper
      config={config}
      settings={loadedSettings}
      startupWarnings={warnings}
    />
  );

  // Handle process termination
  process.on('SIGINT', () => {
    unmount();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    unmount();
    process.exit(0);
  });
}

// Only run main if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('An unexpected critical error occurred:');
    if (error instanceof Error) {
      console.error(error.stack);
    } else {
      console.error(String(error));
    }
    process.exit(1);
  });
}
