{"version": 3, "file": "usePrivacySettings.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/usePrivacySettings.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,QAAQ,CAAC;AACrC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAU,gBAAgB,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAS/E,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,MAAc,EAAE,EAAE;IACnD,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAe;QAC7D,SAAS,EAAE,IAAI;KAChB,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,iBAAiB,GAAG,KAAK,IAAI,EAAE;YACnC,eAAe,CAAC;gBACd,SAAS,EAAE,IAAI;aAChB,CAAC,CAAC;YACH,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAC3C,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC;gBACnC,IAAI,IAAI,KAAK,UAAU,CAAC,IAAI,EAAE,CAAC;oBAC7B,0DAA0D;oBAC1D,uDAAuD;oBACvD,eAAe,CAAC;wBACd,SAAS,EAAE,KAAK;wBAChB,UAAU,EAAE,KAAK;qBAClB,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;gBAED,MAAM,KAAK,GAAG,MAAM,4BAA4B,CAAC,MAAM,CAAC,CAAC;gBACzD,eAAe,CAAC;oBACd,SAAS,EAAE,KAAK;oBAChB,UAAU,EAAE,IAAI;oBAChB,mBAAmB,EAAE,KAAK;iBAC3B,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,eAAe,CAAC;oBACd,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;iBAClD,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;QACF,iBAAiB,EAAE,CAAC;IACtB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,yBAAyB,GAAG,WAAW,CAC3C,KAAK,EAAE,KAAc,EAAE,EAAE;QACvB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,YAAY,GAAG,MAAM,4BAA4B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACvE,eAAe,CAAC;gBACd,SAAS,EAAE,KAAK;gBAChB,UAAU,EAAE,IAAI;gBAChB,mBAAmB,EAAE,YAAY;aAClC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,eAAe,CAAC;gBACd,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;aAClD,CAAC,CAAC;QACL,CAAC;IACH,CAAC,EACD,CAAC,MAAM,CAAC,CACT,CAAC;IAEF,OAAO;QACL,YAAY;QACZ,yBAAyB;KAC1B,CAAC;AACJ,CAAC,CAAC;AAEF,SAAS,mBAAmB,CAAC,MAAc;IACzC,MAAM,MAAM,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,EAAE,CAAC;IAC/D,6CAA6C;IAC7C,IAAI,CAAC,CAAC,MAAM,YAAY,gBAAgB,CAAC,EAAE,CAAC;QAC1C,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC1C,CAAC;SAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC1C,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,OAAO,CAAC,MAAwB;IAC7C,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;QAC1C,uBAAuB,EAAE,MAAM,CAAC,SAAS;QACzC,QAAQ,EAAE;YACR,OAAO,EAAE,iBAAiB;YAC1B,QAAQ,EAAE,sBAAsB;YAChC,UAAU,EAAE,QAAQ;YACpB,WAAW,EAAE,MAAM,CAAC,SAAS;SAC9B;KACF,CAAC,CAAC;IACH,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACzB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC;IACD,OAAO,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;AAChC,CAAC;AAED,KAAK,UAAU,4BAA4B,CACzC,MAAwB;IAExB,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,8BAA8B,EAAE,CAAC;QAC3D,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC1C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,IAAI,CAAC,YAAY,WAAW,EAAE,CAAC;YAC7B,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AAED,KAAK,UAAU,4BAA4B,CACzC,MAAwB,EACxB,KAAc;IAEd,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,8BAA8B,CAAC;QACvD,uBAAuB,EAAE,MAAM,CAAC,SAAS;QACzC,2BAA2B,EAAE,KAAK;KACnC,CAAC,CAAC;IACH,OAAO,IAAI,CAAC,2BAA2B,CAAC;AAC1C,CAAC"}