{"version": 3, "file": "DiffRenderer.test.js", "sourceRoot": "", "sources": ["../../../../../src/ui/components/messages/DiffRenderer.test.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AACrE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,KAAK,aAAa,MAAM,8BAA8B,CAAC;AAC9D,OAAO,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAC;AAE5B,QAAQ,CAAC,uDAAuD,EAAE,GAAG,EAAE;IACrE,MAAM,gBAAgB,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IAEjE,UAAU,CAAC,GAAG,EAAE;QACd,gBAAgB,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,CAAC,MAA0B,EAAE,aAAqB,EAAE,EAAE,CAC3E,MAAM,EAAE,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;IAE/D,EAAE,CAAC,kFAAkF,EAAE,GAAG,EAAE;QAC1F,MAAM,kBAAkB,GAAG;;;;;;;;CAQ9B,CAAC;QACE,MAAM,CACJ,KAAC,gBAAgB,cACf,KAAC,YAAY,IACX,WAAW,EAAE,kBAAkB,EAC/B,QAAQ,EAAC,SAAS,EAClB,aAAa,EAAE,EAAE,GACjB,GACe,CACpB,CAAC;QACF,MAAM,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAC3C,sBAAsB,EACtB,QAAQ,EACR,SAAS,EACT,EAAE,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iFAAiF,EAAE,GAAG,EAAE;QACzF,MAAM,kBAAkB,GAAG;;;;;;;;CAQ9B,CAAC;QACE,MAAM,CACJ,KAAC,gBAAgB,cACf,KAAC,YAAY,IACX,WAAW,EAAE,kBAAkB,EAC/B,QAAQ,EAAC,cAAc,EACvB,aAAa,EAAE,EAAE,GACjB,GACe,CACpB,CAAC;QACF,MAAM,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAC3C,cAAc,EACd,IAAI,EACJ,SAAS,EACT,EAAE,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qFAAqF,EAAE,GAAG,EAAE;QAC7F,MAAM,kBAAkB,GAAG;;;;;;;;CAQ9B,CAAC;QACE,MAAM,CACJ,KAAC,gBAAgB,cACf,KAAC,YAAY,IAAC,WAAW,EAAE,kBAAkB,EAAE,aAAa,EAAE,EAAE,GAAI,GACnD,CACpB,CAAC;QACF,MAAM,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAC3C,mBAAmB,EACnB,IAAI,EACJ,SAAS,EACT,EAAE,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sGAAsG,EAAE,GAAG,EAAE;QAC9G,MAAM,uBAAuB,GAAG;;;;;;;;CAQnC,CAAC;QACE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,YAAY,IACX,WAAW,EAAE,uBAAuB,EACpC,QAAQ,EAAC,UAAU,EACnB,aAAa,EAAE,EAAE,GACjB,GACe,CACpB,CAAC;QACF,yFAAyF;QACzF,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAC/C,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,EACnC,MAAM,CAAC,QAAQ,EAAE,CAClB,CAAC;QACF,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAC/C,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,EACnC,MAAM,CAAC,QAAQ,EAAE,CAClB,CAAC;QACF,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,KAAK,GAAG,MAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;QAC5D,MAAM,YAAY,GAAG;;;;CAIxB,CAAC;QACE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,YAAY,IACX,WAAW,EAAE,YAAY,EACzB,QAAQ,EAAC,UAAU,EACnB,aAAa,EAAE,EAAE,GACjB,GACe,CACpB,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;QACrD,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAC1C,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,YAAY,IAAC,WAAW,EAAC,EAAE,EAAC,aAAa,EAAE,EAAE,GAAI,GACjC,CACpB,CAAC;QACF,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QACjD,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;QACzD,MAAM,WAAW,GAAG;;;;;;;;;;;;CAYvB,CAAC;QACE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,YAAY,IACX,WAAW,EAAE,WAAW,EACxB,QAAQ,EAAC,UAAU,EACnB,aAAa,EAAE,EAAE,GACjB,GACe,CACpB,CAAC;QACF,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,iDAAiD;QAEhF,0DAA0D;QAC1D,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACvC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qFAAqF,EAAE,GAAG,EAAE;QAC7F,MAAM,gBAAgB,GAAG;;;;;;;;;;;;;;;;;CAiB5B,CAAC;QACE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,YAAY,IACX,WAAW,EAAE,gBAAgB,EAC7B,QAAQ,EAAC,UAAU,EACnB,aAAa,EAAE,EAAE,GACjB,GACe,CACpB,CAAC;QACF,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,kCAAkC;QAErE,0DAA0D;QAC1D,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wEAAwE,EAAE,GAAG,EAAE;QACtF,MAAM,qBAAqB,GAAG;;;;;;;;;;;;;;;CAejC,CAAC;QAEE,EAAE,CAAC,IAAI,CAAC;YACN;gBACE,aAAa,EAAE,EAAE;gBACjB,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE;;;;;;;;0CAQwB;aACnC;YACD;gBACE,aAAa,EAAE,EAAE;gBACjB,MAAM,EAAE,CAAC;gBACT,QAAQ,EAAE;;;;;0CAKwB;aACnC;YACD;gBACE,aAAa,EAAE,EAAE;gBACjB,MAAM,EAAE,CAAC;gBACT,QAAQ,EAAE;;;;;sBAKI;aACf;SACF,CAAC,CACA,sDAAsD,EACtD,CAAC,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE;YACtC,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,YAAY,IACX,WAAW,EAAE,qBAAqB,EAClC,QAAQ,EAAC,UAAU,EACnB,aAAa,EAAE,aAAa,EAC5B,uBAAuB,EAAE,MAAM,GAC/B,GACe,CACpB,CAAC;YACF,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;YAC3B,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClE,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;QAC/D,MAAM,WAAW,GAAG;;;;;;;;;;;;;;CAcvB,CAAC;QACE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,YAAY,IACX,WAAW,EAAE,WAAW,EACxB,QAAQ,EAAC,MAAM,EACf,aAAa,EAAE,EAAE,GACjB,GACe,CACpB,CAAC;QACF,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAE3B,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;;;;kCAIO,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;QAC7E,MAAM,WAAW,GAAG;;;;;;;;;;CAUvB,CAAC;QACE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAC1B,KAAC,gBAAgB,cACf,KAAC,YAAY,IACX,WAAW,EAAE,WAAW,EACxB,QAAQ,EAAC,YAAY,EACrB,aAAa,EAAE,EAAE,GACjB,GACe,CACpB,CAAC;QACF,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;;oBAEP,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}