import { jsx as _jsx } from "react/jsx-runtime";
import { render } from 'ink';
import { AppWrapper } from './ui/App.js';
import { loadCliConfig } from './config/config.js';
import { readStdin } from './utils/readStdin.js';
import { start_sandbox } from './utils/sandbox.js';
import { loadSettings, } from './config/settings.js';
import { themeManager } from './ui/themes/theme-manager.js';
import { getStartupWarnings } from './utils/startupWarnings.js';
import { runNonInteractive } from './nonInteractiveCli.js';
import { loadExtensions } from './config/extension.js';
import { cleanupCheckpoints } from './utils/cleanup.js';
import { EditTool, FileDiscoveryService, GitService, initializeTelemetry, MemoryTool, ReadFileTool, ShellTool, ToolRegistry, WriteFileTool, GrepTool, GlobTool, LsTool, WebFetchTool, WebSearchTool, ReadManyFilesTool, MCPClientManager, DiscoveredTool, } from '@arien-ai/arien-ai-core';
export async function main() {
    const startTime = Date.now();
    // Cleanup old checkpoints on startup
    cleanupCheckpoints();
    const args = process.argv.slice(2);
    // Handle version flag
    if (args.includes('--version') || args.includes('-v')) {
        console.log(process.env.CLI_VERSION || 'unknown');
        process.exit(0);
    }
    // Handle help flag
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
Arien-AI CLI - AI-powered command line interface

Usage: arien-ai [options] [prompt]

Options:
  -h, --help              Show this help message
  -v, --version           Show version number
  --non-interactive       Run in non-interactive mode
  --debug                 Enable debug mode
  --approval-mode <mode>  Set approval mode (default, yolo)
  --model <model>         Specify the AI model to use
  --theme <theme>         Set the color theme
  --sandbox <type>        Set sandbox type (docker, podman, false)

Examples:
  arien-ai                           # Start interactive mode
  arien-ai "explain this code"       # Run single command
  arien-ai --debug "analyze logs"    # Run with debug output

For more information, visit: https://github.com/arien-ai/arien-ai-cli
`);
        process.exit(0);
    }
    // Check for non-interactive mode
    const nonInteractiveIndex = args.indexOf('--non-interactive');
    if (nonInteractiveIndex !== -1) {
        args.splice(nonInteractiveIndex, 1);
        const prompt = args.join(' ');
        if (!prompt) {
            console.error('Error: --non-interactive requires a prompt');
            process.exit(1);
        }
        await runNonInteractive(prompt);
        return;
    }
    // Load settings first to determine sandbox configuration
    const loadedSettings = await loadSettings();
    const settings = loadedSettings.settings;
    // Initialize theme manager early
    themeManager.setTheme(settings.theme || 'default');
    // Handle sandbox startup
    if (settings.sandbox !== false && settings.sandbox !== 'false') {
        const sandboxConfig = {
            command: settings.sandbox === true || settings.sandbox === 'true'
                ? 'auto'
                : settings.sandbox || 'auto',
            nodeArgs: process.argv.slice(1), // Skip 'node' but include script path
        };
        try {
            await start_sandbox(sandboxConfig);
            // If we reach here, we're in the parent process and sandbox failed to start
            console.error('Failed to start sandbox');
            process.exit(1);
        }
        catch (error) {
            console.error('Sandbox startup error:', error);
            process.exit(1);
        }
    }
    // Load configuration
    const config = await loadCliConfig(loadedSettings);
    // Initialize telemetry
    initializeTelemetry(config);
    // Load extensions
    const extensions = await loadExtensions(config);
    // Create tool registry
    const toolRegistry = new ToolRegistry();
    // Register core tools
    const fileDiscoveryService = new FileDiscoveryService(config);
    const gitService = new GitService(config);
    const memoryTool = new MemoryTool(config);
    toolRegistry.registerTool(new ReadFileTool(config, fileDiscoveryService));
    toolRegistry.registerTool(new WriteFileTool(config, gitService));
    toolRegistry.registerTool(new EditTool(config, gitService));
    toolRegistry.registerTool(new ShellTool(config));
    toolRegistry.registerTool(new GrepTool(config, fileDiscoveryService));
    toolRegistry.registerTool(new GlobTool(config, fileDiscoveryService));
    toolRegistry.registerTool(new LsTool(config, fileDiscoveryService));
    toolRegistry.registerTool(new WebFetchTool(config));
    toolRegistry.registerTool(new WebSearchTool(config));
    toolRegistry.registerTool(new ReadManyFilesTool(config, fileDiscoveryService));
    toolRegistry.registerTool(memoryTool);
    // Register discovered tools
    const discoveryCommand = config.getToolDiscoveryCommand();
    if (discoveryCommand) {
        try {
            const discoveredTools = await DiscoveredTool.discoverTools(config);
            for (const tool of discoveredTools) {
                toolRegistry.registerTool(tool);
            }
        }
        catch (error) {
            console.warn('Failed to discover tools:', error);
        }
    }
    // Initialize MCP client manager
    const mcpClientManager = new MCPClientManager(config);
    await mcpClientManager.initialize();
    // Register MCP tools
    const mcpTools = await mcpClientManager.getAllTools();
    for (const tool of mcpTools) {
        toolRegistry.registerTool(tool);
    }
    // Get startup warnings
    const warnings = getStartupWarnings(config, loadedSettings);
    // Read stdin if available
    const stdinContent = await readStdin();
    // Calculate startup time
    const startupTime = Date.now() - startTime;
    // Render the app
    const { unmount } = render(_jsx(AppWrapper, { config: config, toolRegistry: toolRegistry, mcpClientManager: mcpClientManager, extensions: extensions, warnings: warnings, stdinContent: stdinContent, startupTime: startupTime }));
    // Handle process termination
    process.on('SIGINT', () => {
        unmount();
        process.exit(0);
    });
    process.on('SIGTERM', () => {
        unmount();
        process.exit(0);
    });
}
// Only run main if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error('An unexpected critical error occurred:');
        if (error instanceof Error) {
            console.error(error.stack);
        }
        else {
            console.error(String(error));
        }
        process.exit(1);
    });
}
//# sourceMappingURL=arien-ai.js.map