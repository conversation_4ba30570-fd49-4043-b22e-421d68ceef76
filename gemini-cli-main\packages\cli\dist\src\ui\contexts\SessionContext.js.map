{"version": 3, "file": "SessionContext.js", "sourceRoot": "", "sources": ["../../../../src/ui/contexts/SessionContext.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EACZ,aAAa,EACb,UAAU,EACV,QAAQ,EACR,OAAO,EACP,WAAW,GACZ,MAAM,OAAO,CAAC;AAkCf,6BAA6B;AAE7B,MAAM,mBAAmB,GAAG,aAAa,CACvC,SAAS,CACV,CAAC;AAEF,2BAA2B;AAE3B;;;;;GAKG;AACH,MAAM,SAAS,GAAG,CAChB,MAAuB,EACvB,MAAqE,EACrE,EAAE;IACF,MAAM,CAAC,oBAAoB,IAAI,MAAM,CAAC,oBAAoB,IAAI,CAAC,CAAC;IAChE,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAC;IAC5D,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,CAAC;IACtD,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;IAC1C,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,uBAAuB,IAAI,MAAM,CAAC,uBAAuB,IAAI,CAAC,CAAC;IACtE,MAAM,CAAC,uBAAuB,IAAI,MAAM,CAAC,uBAAuB,IAAI,CAAC,CAAC;AACxE,CAAC,CAAC;AAEF,6BAA6B;AAE7B,MAAM,CAAC,MAAM,oBAAoB,GAA4C,CAAC,EAC5E,QAAQ,GACT,EAAE,EAAE;IACH,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAoB;QACpD,gBAAgB,EAAE,IAAI,IAAI,EAAE;QAC5B,UAAU,EAAE;YACV,SAAS,EAAE,CAAC;YACZ,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,eAAe,EAAE,CAAC;YAClB,uBAAuB,EAAE,CAAC;YAC1B,uBAAuB,EAAE,CAAC;YAC1B,kBAAkB,EAAE,CAAC;YACrB,SAAS,EAAE,CAAC;SACb;QACD,WAAW,EAAE;YACX,SAAS,EAAE,CAAC;YACZ,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,eAAe,EAAE,CAAC;YAClB,uBAAuB,EAAE,CAAC;YAC1B,uBAAuB,EAAE,CAAC;YAC1B,kBAAkB,EAAE,CAAC;YACrB,SAAS,EAAE,CAAC;SACb;QACD,eAAe,EAAE;YACf,SAAS,EAAE,CAAC;YACZ,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,eAAe,EAAE,CAAC;YAClB,uBAAuB,EAAE,CAAC;YAC1B,uBAAuB,EAAE,CAAC;YAC1B,kBAAkB,EAAE,CAAC;YACrB,SAAS,EAAE,CAAC;SACb;KACF,CAAC,CAAC;IAEH,yEAAyE;IACzE,MAAM,eAAe,GAAG,WAAW,CACjC,CACE,QAAuE,EACvE,EAAE;QACF,QAAQ,CAAC,CAAC,SAAS,EAAE,EAAE;YACrB,MAAM,aAAa,GAAG,EAAE,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;YAClD,MAAM,cAAc,GAAG,EAAE,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,kBAAkB,GAAG;gBACzB,SAAS,EAAE,CAAC;gBACZ,gBAAgB,EAAE,CAAC;gBACnB,oBAAoB,EAAE,CAAC;gBACvB,eAAe,EAAE,CAAC;gBAClB,uBAAuB,EAAE,CAAC;gBAC1B,uBAAuB,EAAE,CAAC;gBAC1B,kBAAkB,EAAE,CAAC;gBACrB,SAAS,EAAE,CAAC;aACb,CAAC;YAEF,0EAA0E;YAC1E,SAAS,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YACpC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YACnC,SAAS,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAExC,OAAO;gBACL,GAAG,SAAS;gBACZ,UAAU,EAAE,aAAa;gBACzB,WAAW,EAAE,cAAc;gBAC3B,eAAe,EAAE,kBAAkB;aACpC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,EACD,EAAE,CACH,CAAC;IAEF,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,QAAQ,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YACvB,GAAG,SAAS;YACZ,UAAU,EAAE;gBACV,GAAG,SAAS,CAAC,UAAU;gBACvB,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC;aAC9C;YACD,WAAW,EAAE;gBACX,SAAS,EAAE,CAAC,EAAE,yCAAyC;gBACvD,gBAAgB,EAAE,CAAC;gBACnB,oBAAoB,EAAE,CAAC;gBACvB,eAAe,EAAE,CAAC;gBAClB,uBAAuB,EAAE,CAAC;gBAC1B,uBAAuB,EAAE,CAAC;gBAC1B,kBAAkB,EAAE,CAAC;gBACrB,SAAS,EAAE,CAAC;aACb;YACD,eAAe,EAAE;gBACf,SAAS,EAAE,CAAC;gBACZ,gBAAgB,EAAE,CAAC;gBACnB,oBAAoB,EAAE,CAAC;gBACvB,eAAe,EAAE,CAAC;gBAClB,uBAAuB,EAAE,CAAC;gBAC1B,uBAAuB,EAAE,CAAC;gBAC1B,kBAAkB,EAAE,CAAC;gBACrB,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC,CAAC;IACN,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,KAAK,GAAG,OAAO,CACnB,GAAG,EAAE,CAAC,CAAC;QACL,KAAK;QACL,YAAY;QACZ,QAAQ,EAAE,eAAe;KAC1B,CAAC,EACF,CAAC,KAAK,EAAE,YAAY,EAAE,eAAe,CAAC,CACvC,CAAC;IAEF,OAAO,CACL,KAAC,mBAAmB,CAAC,QAAQ,IAAC,KAAK,EAAE,KAAK,YACvC,QAAQ,GACoB,CAChC,CAAC;AACJ,CAAC,CAAC;AAEF,wBAAwB;AAExB,MAAM,CAAC,MAAM,eAAe,GAAG,GAAG,EAAE;IAClC,MAAM,OAAO,GAAG,UAAU,CAAC,mBAAmB,CAAC,CAAC;IAChD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CACb,4DAA4D,CAC7D,CAAC;IACJ,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC"}