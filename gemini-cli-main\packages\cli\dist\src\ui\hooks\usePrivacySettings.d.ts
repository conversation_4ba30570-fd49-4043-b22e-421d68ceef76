/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Config } from '@arien-ai/arien-ai-core';
export interface PrivacyState {
    isLoading: boolean;
    error?: string;
    isFreeTier?: boolean;
    dataCollectionOptIn?: boolean;
}
export declare const usePrivacySettings: (config: Config) => {
    privacyState: PrivacyState;
    updateDataCollectionOptIn: (optIn: boolean) => Promise<void>;
};
